
cc.Class({
    extends: cc.Component,

    properties: {
     enemyprafab:cc.Prefab,
        enemyJson:cc.JsonAsset,

    },

    // LIFE-CYCLE CALLBACKS:

    onLoad () {
        this.schedule(this.createEnemy, 2);
        // this.createEnemy();
        // this.schedule(this.enemyfireBullet,0.5);
    },

    start () {

    },

    // update (dt) {},
    createEnemy(){

         let enemynode = cc.instantiate(this.enemyprafab);
        //  this.node.addChild(enemynode);
        enemynode.parent = this.node;

        let minX = -cc.winSize.width /2 +enemynode.width /2;
        let maxX = cc.winSize.width /2 - enemynode.width /2;
        let x =Math.random()*(maxX - minX ) + minX;
        enemynode.x = x;
        enemynode.y = cc.winSize.height /2;


        let index = Math.floor(Math.random() * (4-0)+0);
        let enemyDt = this.enemyJson.json[index];
    

//生成不同敌人
//
        let enemyJs=enemynode.getComponent("Enemy");
        enemyJs.initWithData(enemyDt);
        enemyJs.move();
        
    },


});
